"use client";

/**
 * DeadlinesPanel Component for AG-UI Integration
 * 
 * This component displays legal deadlines calculated by the MCP Rules Engine
 * through the Deadline Agent. It subscribes to CopilotKit messages and renders
 * deadline results in a sortable table format.
 */

import React, { useState, useEffect, useMemo } from 'react';
import { useCopilotReadable, useCopilotAction } from '@copilotkit/react-core';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  CalendarDays, 
  AlertTriangle, 
  Clock, 
  FileText, 
  Scale,
  ArrowUpDown,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Types for deadline data
interface Deadline {
  id: string;
  name: string;
  dueDate: string;
  priority: 'low' | 'medium' | 'high';
  category: string;
  description?: string;
  legalBasis?: string;
  consequences?: string;
}

interface DeadlineMetadata {
  jurisdiction: string;
  triggerCode: string;
  startDate: string;
  practiceArea: string;
  calculatedAt: string;
  source: string;
}

interface DeadlinesData {
  deadlines: Deadline[];
  metadata: DeadlineMetadata;
}

type SortField = 'name' | 'dueDate' | 'priority' | 'category';
type SortDirection = 'asc' | 'desc';

export function DeadlinesPanel() {
  const [deadlinesData, setDeadlinesData] = useState<DeadlinesData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sortField, setSortField] = useState<SortField>('dueDate');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');

  // Make deadlines data readable by CopilotKit
  useCopilotReadable({
    description: "Current legal deadlines data",
    value: deadlinesData,
  });

  // Register action to fetch deadlines
  useCopilotAction({
    name: "fetchDeadlines",
    description: "Fetch legal deadlines for a case",
    parameters: [
      {
        name: "jurisdiction",
        type: "string",
        description: "Legal jurisdiction (e.g., TX_STATE)",
        required: true,
      },
      {
        name: "triggerCode", 
        type: "string",
        description: "Trigger event code (e.g., SERVICE_OF_PROCESS)",
        required: true,
      },
      {
        name: "startDate",
        type: "string", 
        description: "Start date in YYYY-MM-DD format",
        required: true,
      },
      {
        name: "practiceArea",
        type: "string",
        description: "Practice area (e.g., personal_injury)",
        required: false,
      },
    ],
    handler: async ({ jurisdiction, triggerCode, startDate, practiceArea }) => {
      setIsLoading(true);
      setError(null);
      
      try {
        // This would trigger the deadline agent through CopilotKit
        // The actual implementation depends on your CopilotKit setup
        console.log('Fetching deadlines:', { jurisdiction, triggerCode, startDate, practiceArea });
        
        // For now, return a message that will trigger the agent
        return `Calculate deadlines for ${jurisdiction} jurisdiction, trigger: ${triggerCode}, start date: ${startDate}, practice area: ${practiceArea || 'personal_injury'}`;
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch deadlines');
        return 'Error fetching deadlines';
      } finally {
        setIsLoading(false);
      }
    },
  });

  // Listen for deadline results from CopilotKit messages
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      try {
        const data = event.data;
        
        // Check if this is a deadline results message
        if (data?.role === 'deadline_results' && data?.deadlines) {
          setDeadlinesData({
            deadlines: data.deadlines,
            metadata: data.metadata || {},
          });
          setIsLoading(false);
          setError(null);
        } else if (data?.role === 'deadline_error') {
          setError(data.error || 'Unknown error occurred');
          setIsLoading(false);
        }
      } catch (err) {
        console.error('Error processing deadline message:', err);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // Sort deadlines
  const sortedDeadlines = useMemo(() => {
    if (!deadlinesData?.deadlines) return [];

    return [...deadlinesData.deadlines].sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortField) {
        case 'dueDate':
          aValue = new Date(a.dueDate).getTime();
          bValue = new Date(b.dueDate).getTime();
          break;
        case 'priority':
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          aValue = priorityOrder[a.priority];
          bValue = priorityOrder[b.priority];
          break;
        default:
          aValue = a[sortField].toLowerCase();
          bValue = b[sortField].toLowerCase();
      }

      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
  }, [deadlinesData?.deadlines, sortField, sortDirection]);

  // Handle sort
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Get sort icon
  const getSortIcon = (field: SortField) => {
    if (sortField !== field) {
      return <ArrowUpDown className="h-4 w-4" />;
    }
    return sortDirection === 'asc' ? 
      <ArrowUp className="h-4 w-4" /> : 
      <ArrowDown className="h-4 w-4" />;
  };

  // Get priority badge variant
  const getPriorityVariant = (priority: string) => {
    switch (priority) {
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'outline';
    }
  };

  // Get priority icon
  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return '🔴';
      case 'medium': return '🟡';
      case 'low': return '🟢';
      default: return '⚪';
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
      });
    } catch {
      return dateString;
    }
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CalendarDays className="h-5 w-5" />
          Legal Deadlines
        </CardTitle>
        <CardDescription>
          Statutory deadlines and filing requirements calculated by MCP Rules Engine
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {isLoading && (
          <div className="space-y-3">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        )}

        {deadlinesData && (
          <>
            {/* Metadata */}
            <div className="mb-4 p-3 bg-muted rounded-lg">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                <div>
                  <span className="font-medium">Jurisdiction:</span> {deadlinesData.metadata.jurisdiction}
                </div>
                <div>
                  <span className="font-medium">Trigger:</span> {deadlinesData.metadata.triggerCode}
                </div>
                <div>
                  <span className="font-medium">Start Date:</span> {formatDate(deadlinesData.metadata.startDate)}
                </div>
                <div>
                  <span className="font-medium">Practice Area:</span> {deadlinesData.metadata.practiceArea}
                </div>
              </div>
            </div>

            {/* Deadlines Table */}
            {sortedDeadlines.length > 0 ? (
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSort('name')}
                          className="h-auto p-0 font-medium"
                        >
                          Deadline {getSortIcon('name')}
                        </Button>
                      </TableHead>
                      <TableHead>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSort('dueDate')}
                          className="h-auto p-0 font-medium"
                        >
                          Due Date {getSortIcon('dueDate')}
                        </Button>
                      </TableHead>
                      <TableHead>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSort('priority')}
                          className="h-auto p-0 font-medium"
                        >
                          Priority {getSortIcon('priority')}
                        </Button>
                      </TableHead>
                      <TableHead>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSort('category')}
                          className="h-auto p-0 font-medium"
                        >
                          Category {getSortIcon('category')}
                        </Button>
                      </TableHead>
                      <TableHead>Legal Basis</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedDeadlines.map((deadline) => (
                      <TableRow key={deadline.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{deadline.name}</div>
                            {deadline.description && (
                              <div className="text-sm text-muted-foreground mt-1">
                                {deadline.description}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-muted-foreground" />
                            {formatDate(deadline.dueDate)}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getPriorityVariant(deadline.priority)}>
                            {getPriorityIcon(deadline.priority)} {deadline.priority}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{deadline.category}</Badge>
                        </TableCell>
                        <TableCell>
                          {deadline.legalBasis && (
                            <div className="flex items-center gap-2">
                              <Scale className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm">{deadline.legalBasis}</span>
                            </div>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No deadlines found for the specified criteria.</p>
              </div>
            )}

            {/* Footer */}
            <div className="mt-4 text-xs text-muted-foreground text-center">
              Calculated at {new Date(deadlinesData.metadata.calculatedAt).toLocaleString()} • 
              Source: {deadlinesData.metadata.source}
            </div>
          </>
        )}

        {!deadlinesData && !isLoading && !error && (
          <div className="text-center py-8 text-muted-foreground">
            <CalendarDays className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Ask me to calculate deadlines for your case.</p>
            <p className="text-sm mt-2">
              Example: "Calculate deadlines for Texas service of process on 2025-01-15"
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default DeadlinesPanel;
