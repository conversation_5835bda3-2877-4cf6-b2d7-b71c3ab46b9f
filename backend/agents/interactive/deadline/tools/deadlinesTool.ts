/**
 * Deadlines Tool for MCP Rules Engine Integration
 * 
 * This tool provides deadline calculation functionality by integrating with the MCP Rules Engine.
 * It validates input parameters and returns structured deadline information.
 */

import { z } from 'zod';
import { tool } from '@langchain/core/tools';
import { McpClient } from '@ailex/mcp-client';
import { createClient } from '@supabase/supabase-js';
import Redis from 'ioredis';

// Environment variables
const MCP_RULES_BASE = process.env.MCP_RULES_BASE || 'https://pi-lawyer-mcp-rules-k46fd4vfq-jpkays-projects.vercel.app';
const MCP_API_KEY = process.env.MCP_API_KEY || '';
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';

// Initialize clients
const mcpClient = new McpClient({
  baseUrl: MCP_RULES_BASE,
  apiKey: MCP_API_KEY,
});

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);
const redis = new Redis(REDIS_URL);

// Input validation schema
const deadlinesInputSchema = z.object({
  jurisdiction: z.string().min(1, 'Jurisdiction is required'),
  triggerCode: z.string().min(1, 'Trigger code is required'),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'),
  practiceArea: z.string().optional().default('personal_injury'),
});

// Output schema for type safety
const deadlineSchema = z.object({
  id: z.string(),
  name: z.string(),
  dueDate: z.string(),
  priority: z.enum(['low', 'medium', 'high']),
  category: z.string(),
  description: z.string().optional(),
  legalBasis: z.string().optional(),
  consequences: z.string().optional(),
});

const deadlinesOutputSchema = z.object({
  deadlines: z.array(deadlineSchema),
  jurisdiction: z.string(),
  triggerCode: z.string(),
  startDate: z.string(),
  practiceArea: z.string(),
  calculatedAt: z.string(),
  source: z.literal('mcp_rules_engine'),
});

export type DeadlinesInput = z.infer<typeof deadlinesInputSchema>;
export type DeadlinesOutput = z.infer<typeof deadlinesOutputSchema>;

/**
 * Generate cache key for Redis
 */
function generateCacheKey(input: DeadlinesInput): string {
  const { jurisdiction, triggerCode, startDate, practiceArea } = input;
  return `deadlines:${jurisdiction}:${triggerCode}:${startDate}:${practiceArea}`;
}

/**
 * Calculate deadlines using MCP Rules Engine
 */
async function calculateDeadlines(input: DeadlinesInput): Promise<DeadlinesOutput> {
  const cacheKey = generateCacheKey(input);
  
  try {
    // Check Redis cache first (10 minute TTL)
    const cached = await redis.get(cacheKey);
    if (cached) {
      console.log(`Cache hit for deadlines: ${cacheKey}`);
      return JSON.parse(cached);
    }

    console.log(`Cache miss, calling MCP Rules Engine: ${cacheKey}`);
    
    // Call MCP Rules Engine
    const mcpResponse = await mcpClient.calculateDeadlines(
      input.jurisdiction,
      input.triggerCode,
      input.startDate,
      input.practiceArea
    );

    // Transform MCP response to our schema
    const result: DeadlinesOutput = {
      deadlines: mcpResponse.deadlines?.map((deadline: any) => ({
        id: deadline.id || `deadline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: deadline.name || deadline.description || 'Unnamed Deadline',
        dueDate: deadline.dueDate || deadline.date,
        priority: deadline.priority || 'medium',
        category: deadline.category || 'statutory',
        description: deadline.description,
        legalBasis: deadline.legalBasis || deadline.citation,
        consequences: deadline.consequences,
      })) || [],
      jurisdiction: input.jurisdiction,
      triggerCode: input.triggerCode,
      startDate: input.startDate,
      practiceArea: input.practiceArea,
      calculatedAt: new Date().toISOString(),
      source: 'mcp_rules_engine',
    };

    // Cache result for 10 minutes
    await redis.setex(cacheKey, 600, JSON.stringify(result));
    
    console.log(`Calculated ${result.deadlines.length} deadlines for ${input.jurisdiction}`);
    return result;

  } catch (error) {
    console.error('Error calculating deadlines:', error);
    
    // Return empty result on error
    return {
      deadlines: [],
      jurisdiction: input.jurisdiction,
      triggerCode: input.triggerCode,
      startDate: input.startDate,
      practiceArea: input.practiceArea,
      calculatedAt: new Date().toISOString(),
      source: 'mcp_rules_engine',
    };
  }
}

/**
 * Deadlines Tool Definition
 */
export const deadlinesTool = tool(
  calculateDeadlines,
  {
    name: 'calculate_deadlines',
    description: 'Calculate statutory deadlines given jurisdiction, trigger event, date, and practice area. Returns a list of applicable deadlines with due dates, priorities, and legal basis.',
    schema: deadlinesInputSchema,
  }
);

export default deadlinesTool;
