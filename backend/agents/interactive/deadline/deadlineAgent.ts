/**
 * Deadline Agent for CopilotKit Integration
 * 
 * This agent handles deadline calculation requests by integrating with the MCP Rules Engine.
 * It processes user requests, calls the deadlines tool, and emits structured responses for AG-UI.
 */

import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { RunnableConfig } from '@langchain/core/runnables';
import { ChatOpenAI } from '@langchain/openai';
import { deadlinesTool, DeadlinesInput, DeadlinesOutput } from './tools/deadlinesTool';

// State interface for the deadline agent
export interface DeadlineAgentState {
  messages: (HumanMessage | AIMessage)[];
  deadlinesResult?: DeadlinesOutput;
  error?: string;
}

// Initialize LLM
const llm = new ChatOpenAI({
  modelName: process.env.OPENAI_MODEL || 'gpt-4',
  temperature: 0.1,
  openAIApiKey: process.env.OPENAI_API_KEY,
});

/**
 * Extract deadline parameters from user message
 */
function extractDeadlineParameters(message: string): Partial<DeadlinesInput> {
  const params: Partial<DeadlinesInput> = {};
  
  // Extract jurisdiction (common patterns)
  const jurisdictionPatterns = [
    /(?:jurisdiction|state|in)\s+([A-Z]{2}(?:_STATE)?)/i,
    /(?:texas|tx)/i,
    /(?:california|ca)/i,
    /(?:new york|ny)/i,
    /(?:florida|fl)/i,
  ];
  
  for (const pattern of jurisdictionPatterns) {
    const match = message.match(pattern);
    if (match) {
      if (match[1]) {
        params.jurisdiction = match[1].toUpperCase();
      } else if (pattern.source.includes('texas|tx')) {
        params.jurisdiction = 'TX_STATE';
      } else if (pattern.source.includes('california|ca')) {
        params.jurisdiction = 'CA_STATE';
      } else if (pattern.source.includes('new york|ny')) {
        params.jurisdiction = 'NY_STATE';
      } else if (pattern.source.includes('florida|fl')) {
        params.jurisdiction = 'FL_STATE';
      }
      break;
    }
  }
  
  // Extract trigger code (common patterns)
  const triggerPatterns = [
    /service of process/i,
    /complaint filed/i,
    /discovery request/i,
    /deposition notice/i,
    /motion filed/i,
    /accident date/i,
    /injury date/i,
  ];
  
  const triggerMap: Record<string, string> = {
    'service of process': 'SERVICE_OF_PROCESS',
    'complaint filed': 'COMPLAINT_FILED',
    'discovery request': 'DISCOVERY_REQUEST',
    'deposition notice': 'DEPOSITION_NOTICE',
    'motion filed': 'MOTION_FILED',
    'accident date': 'ACCIDENT_DATE',
    'injury date': 'INJURY_DATE',
  };
  
  for (const pattern of triggerPatterns) {
    if (pattern.test(message)) {
      const key = pattern.source.replace(/[\/\\]/g, '').toLowerCase();
      params.triggerCode = triggerMap[key] || 'SERVICE_OF_PROCESS';
      break;
    }
  }
  
  // Extract date (YYYY-MM-DD format)
  const datePattern = /(\d{4}-\d{2}-\d{2})/;
  const dateMatch = message.match(datePattern);
  if (dateMatch) {
    params.startDate = dateMatch[1];
  }
  
  // Extract practice area
  if (/personal injury|PI|auto accident|slip and fall/i.test(message)) {
    params.practiceArea = 'personal_injury';
  } else if (/medical malpractice|malpractice/i.test(message)) {
    params.practiceArea = 'medical_malpractice';
  } else if (/workers compensation|workers comp/i.test(message)) {
    params.practiceArea = 'workers_compensation';
  }
  
  return params;
}

/**
 * Validate and complete deadline parameters
 */
function validateDeadlineParameters(params: Partial<DeadlinesInput>): DeadlinesInput | null {
  // Set defaults
  const defaults: DeadlinesInput = {
    jurisdiction: 'TX_STATE',
    triggerCode: 'SERVICE_OF_PROCESS',
    startDate: new Date().toISOString().split('T')[0], // Today's date
    practiceArea: 'personal_injury',
  };
  
  const result = { ...defaults, ...params };
  
  // Validate required fields
  if (!result.jurisdiction || !result.triggerCode || !result.startDate) {
    return null;
  }
  
  // Validate date format
  if (!/^\d{4}-\d{2}-\d{2}$/.test(result.startDate)) {
    return null;
  }
  
  return result;
}

/**
 * Main deadline agent node
 */
export async function deadlineAgent(
  state: DeadlineAgentState,
  config: RunnableConfig
): Promise<Partial<DeadlineAgentState>> {
  try {
    console.log('DeadlineAgent: Processing request');
    
    // Get the latest user message
    const lastMessage = state.messages[state.messages.length - 1];
    if (!lastMessage || lastMessage._getType() !== 'human') {
      throw new Error('No user message found');
    }
    
    const userMessage = lastMessage.content as string;
    console.log('DeadlineAgent: User message:', userMessage);
    
    // Extract parameters from user message
    const extractedParams = extractDeadlineParameters(userMessage);
    console.log('DeadlineAgent: Extracted parameters:', extractedParams);
    
    // Validate and complete parameters
    const validParams = validateDeadlineParameters(extractedParams);
    if (!validParams) {
      throw new Error('Could not extract valid deadline parameters from message');
    }
    
    console.log('DeadlineAgent: Valid parameters:', validParams);
    
    // Call the deadlines tool
    const deadlinesResult = await deadlinesTool.invoke(validParams);
    console.log('DeadlineAgent: Tool result:', deadlinesResult);
    
    // Create response message
    const responseContent = {
      role: 'deadline_results',
      data: deadlinesResult,
      message: `Found ${deadlinesResult.deadlines.length} deadlines for ${validParams.jurisdiction} jurisdiction`,
    };
    
    const responseMessage = new AIMessage({
      content: JSON.stringify(responseContent),
      additional_kwargs: {
        role: 'deadline_results',
        deadlines: deadlinesResult.deadlines,
        metadata: {
          jurisdiction: deadlinesResult.jurisdiction,
          triggerCode: deadlinesResult.triggerCode,
          startDate: deadlinesResult.startDate,
          practiceArea: deadlinesResult.practiceArea,
          calculatedAt: deadlinesResult.calculatedAt,
          source: deadlinesResult.source,
        },
      },
    });
    
    console.log('DeadlineAgent: Response created');
    
    return {
      messages: [responseMessage],
      deadlinesResult,
    };
    
  } catch (error) {
    console.error('DeadlineAgent: Error processing request:', error);
    
    const errorMessage = new AIMessage({
      content: JSON.stringify({
        role: 'deadline_error',
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Sorry, I encountered an error while calculating deadlines. Please try again with more specific information.',
      }),
    });
    
    return {
      messages: [errorMessage],
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Check if message should trigger deadline agent
 */
export function shouldTriggerDeadlineAgent(message: string): boolean {
  const triggers = [
    /deadline/i,
    /due date/i,
    /statute of limitations/i,
    /filing deadline/i,
    /discovery deadline/i,
    /court deadline/i,
    /legal deadline/i,
    /time limit/i,
    /when is.*due/i,
    /calculate.*deadline/i,
    /show.*deadline/i,
    /find.*deadline/i,
  ];
  
  return triggers.some(trigger => trigger.test(message));
}

export default deadlineAgent;
